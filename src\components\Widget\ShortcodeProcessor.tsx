"use client";

import { useMemo } from "react";
import dynamic from "next/dynamic";

// Dynamically import PDF viewer to avoid SSR issues
const ComprehensivePDFViewer = dynamic(() => import("@/components/Widget/ComprehensivePDFViewer"), { ssr: false });

interface ShortcodeProcessorProps {
  html: string;
}

interface PDFShortcode {
  url: string;
  fileName?: string;
  viewer?: 'advanced' | 'react-pdf' | 'browser';
}

const ShortcodeProcessor = ({ html }: ShortcodeProcessorProps) => {
  const processedContent = useMemo(() => {
    // Regular expression to match PDF shortcodes
    // Format: [pdf url="..." fileName="..." viewer="..."]
    const pdfShortcodeRegex = /\[pdf\s+([^\]]+)\]/g;
    
    const parts: (string | JSX.Element)[] = [];
    let lastIndex = 0;
    let match;
    let keyCounter = 0;

    while ((match = pdfShortcodeRegex.exec(html)) !== null) {
      // Add content before the shortcode
      if (match.index > lastIndex) {
        const beforeContent = html.slice(lastIndex, match.index);
        if (beforeContent.trim()) {
          parts.push(
            <div 
              key={`content-${keyCounter++}`}
              dangerouslySetInnerHTML={{ __html: beforeContent }} 
            />
          );
        }
      }

      // Parse shortcode attributes
      const attributesString = match[1];
      const pdfData = parseShortcodeAttributes(attributesString);

      if (pdfData.url) {
        parts.push(
          <ComprehensivePDFViewer
            key={`pdf-${keyCounter++}`}
            fileUrl={pdfData.url}
            fileName={pdfData.fileName}
          />
        );
      }

      lastIndex = match.index + match[0].length;
    }

    // Add remaining content after the last shortcode
    if (lastIndex < html.length) {
      const remainingContent = html.slice(lastIndex);
      if (remainingContent.trim()) {
        parts.push(
          <div 
            key={`content-${keyCounter++}`}
            dangerouslySetInnerHTML={{ __html: remainingContent }} 
          />
        );
      }
    }

    // If no shortcodes found, return original HTML
    if (parts.length === 0) {
      return <div dangerouslySetInnerHTML={{ __html: html }} />;
    }

    return <>{parts}</>;
  }, [html]);

  return <>{processedContent}</>;
};

// Helper function to parse shortcode attributes
function parseShortcodeAttributes(attributesString: string): PDFShortcode {
  const result: PDFShortcode = { url: '' };
  
  // Regular expressions to match different attribute formats
  const urlMatch = attributesString.match(/url=["']([^"']+)["']/);
  const fileNameMatch = attributesString.match(/fileName=["']([^"']+)["']/);
  const viewerMatch = attributesString.match(/viewer=["']([^"']+)["']/);

  if (urlMatch) {
    result.url = urlMatch[1];
  }
  
  if (fileNameMatch) {
    result.fileName = fileNameMatch[1];
  }
  
  if (viewerMatch && ['advanced', 'react-pdf', 'browser'].includes(viewerMatch[1])) {
    result.viewer = viewerMatch[1] as 'advanced' | 'react-pdf' | 'browser';
  }

  return result;
}

export default ShortcodeProcessor;
