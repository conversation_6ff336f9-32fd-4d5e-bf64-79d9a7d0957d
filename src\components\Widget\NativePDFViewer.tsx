"use client";

import { useState } from "react";

interface NativePDFViewerProps {
  fileUrl: string;
  fileName?: string;
}

const NativePDFViewer = ({ fileUrl, fileName }: NativePDFViewerProps) => {
  const [viewerMethod, setViewerMethod] = useState<'google' | 'mozilla' | 'direct' | 'download'>('google');
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Different PDF viewing methods
  const getViewerUrl = () => {
    switch (viewerMethod) {
      case 'google':
        return `https://docs.google.com/gview?embedded=true&url=${encodeURIComponent(fileUrl)}`;
      case 'mozilla':
        return `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(fileUrl)}`;
      case 'direct':
        return fileUrl;
      default:
        return fileUrl;
    }
  };

  const handleLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  const renderViewer = () => {
    if (viewerMethod === 'download') {
      return (
        <div className="text-center py-16 bg-gray-50 rounded-lg">
          <svg className="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-4-4m4 4l4-4m-4-4V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Tải xuống PDF</h3>
          <p className="text-gray-600 mb-6">
            Nhấp vào nút bên dưới để tải xuống và xem PDF
          </p>
          <div className="space-y-3">
            <a
              href={fileUrl}
              download={fileName}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-4-4m4 4l4-4m-4-4V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2" />
              </svg>
              Tải xuống PDF
            </a>
            <div>
              <a
                href={fileUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                Mở trong tab mới
              </a>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="relative bg-white rounded-lg overflow-hidden" style={{ height: '700px' }}>
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-20">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Đang tải PDF...</p>
              <p className="text-sm text-gray-500 mt-2">
                Phương thức: {viewerMethod === 'google' ? 'Google Docs' : viewerMethod === 'mozilla' ? 'Mozilla PDF.js' : 'Trực tiếp'}
              </p>
            </div>
          </div>
        )}

        {hasError && (
          <div className="absolute inset-0 flex items-center justify-center bg-red-50 z-20">
            <div className="text-center max-w-md">
              <svg className="mx-auto h-12 w-12 text-red-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <h3 className="text-lg font-medium text-red-900 mb-2">Không thể tải PDF</h3>
              <p className="text-red-700 mb-4">
                Thử chuyển sang phương thức khác hoặc mở trực tiếp
              </p>
              <div className="space-x-3">
                <button
                  onClick={() => {
                    setHasError(false);
                    setIsLoading(true);
                    setViewerMethod(viewerMethod === 'google' ? 'mozilla' : 'google');
                  }}
                  className="inline-flex items-center px-3 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50"
                >
                  Thử phương thức khác
                </button>
                <a
                  href={fileUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
                >
                  Mở trực tiếp
                </a>
              </div>
            </div>
          </div>
        )}

        <iframe
          src={getViewerUrl()}
          className="w-full h-full border-0"
          onLoad={handleLoad}
          onError={handleError}
          title={fileName || 'PDF Viewer'}
          allow="fullscreen"
          sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
        />
      </div>
    );
  };

  return (
    <div className="w-full my-6">
      <div className="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
        {/* Header with controls */}
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <h3 className="text-sm font-semibold text-gray-900">
                PDF Viewer - Không cần Extension
              </h3>
              {fileName && (
                <p className="text-xs text-gray-600 mt-1 truncate">{fileName}</p>
              )}
            </div>
            
            <div className="flex items-center space-x-3">
              {/* Method selector */}
              <div className="flex items-center space-x-2">
                <label className="text-xs font-medium text-gray-700">Phương thức:</label>
                <select
                  value={viewerMethod}
                  onChange={(e) => {
                    setViewerMethod(e.target.value as any);
                    setIsLoading(true);
                    setHasError(false);
                  }}
                  className="text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="google">Google Docs</option>
                  <option value="mozilla">Mozilla PDF.js</option>
                  <option value="direct">Trực tiếp</option>
                  <option value="download">Tải xuống</option>
                </select>
              </div>
              
              {/* External link */}
              <a
                href={fileUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-blue-500"
                title="Mở trong tab mới"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </a>
            </div>
          </div>
        </div>

        {/* PDF Viewer */}
        {renderViewer()}

        {/* Footer with info */}
        <div className="bg-gray-50 px-4 py-2 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <p className="text-xs text-gray-600 truncate flex-1 mr-4">
              <span className="font-medium">URL:</span> {fileUrl}
            </p>
            <div className="flex items-center space-x-2">
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                Không cần Extension
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NativePDFViewer;
