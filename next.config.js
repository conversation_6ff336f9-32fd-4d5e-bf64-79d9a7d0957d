/** @type {import('next').NextConfig} */
module.exports = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "tand.hochiminhcity.gov.vn",
      },
      {
        protocol: "http",
        hostname: "tand.hochiminhcity.gov.vn",
      },
      {
        protocol: "https",
        hostname: "localhost",
        port: "8000",
      },
      {
        protocol: "http",
        hostname: "localhost",
        port: "8000",
      },
    ],
    formats: ["image/avif", "image/webp"],
  },
  env: {
    NEXT_PUBLIC_SITE_NAME: "TANDHCM",
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Disable dev indicators
  devIndicators: false,
  // Additional security configurations
  poweredByHeader: false,
  compress: true,
  // API routes timeout configuration
  experimental: {
    serverComponentsExternalPackages: [],
  },
  // Increase server timeout for API routes
  serverTimeout: 20000, // 20 seconds

  // Headers configuration for CSP and PDF viewing
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN',
          },
          {
            key: 'Content-Security-Policy',
            value: "frame-src 'self' data: blob: http://localhost:8000 https://localhost:8000 https://docs.google.com https://view.officeapps.live.com; frame-ancestors 'self'; object-src 'self' data: blob: http://localhost:8000 https://localhost:8000;",
          },
        ],
      },
    ];
  },
};