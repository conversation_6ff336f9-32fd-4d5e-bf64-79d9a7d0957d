import dynamic from "next/dynamic";

// Dynamically import components to avoid SSR issues
const PDFShortcodeGuide = dynamic(() => import("@/components/Widget/PDFShortcodeGuide"), { ssr: false });
const ShortcodeProcessor = dynamic(() => import("@/components/Widget/ShortcodeProcessor"), { ssr: false });

export default function PDFBlogDemo() {
  // Sample blog content with PDF shortcodes
  const sampleContent = `
    <h2>Demo PDF Shortcode trong Blog</h2>
    <p>Đây là trang demo để test tính năng PDF shortcode. Bạn có thể sử dụng các shortcode sau để hiển thị PDF trong bài viết:</p>
    
    <h3>1. PDF với Chrome Extension (mặc định)</h3>
    <p>Shortcode này sẽ sử dụng Chrome extension để xem PDF:</p>
    [pdf url="https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf" fileName="Dummy PDF Document"]
    
    <h3>2. PDF với React PDF Viewer</h3>
    <p>Shortcode này sẽ sử dụng React PDF để hiển thị PDF tích hợp trong trang:</p>
    [pdf url="https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf" fileName="React PDF Demo" viewer="react-pdf"]
    
    <h3>3. PDF mở trong trình duyệt</h3>
    <p>Shortcode này sẽ mở PDF trong tab mới của trình duyệt:</p>
    [pdf url="https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf" fileName="Browser PDF Demo" viewer="browser"]
    
    <h3>4. PDF từ local server</h3>
    <p>Ví dụ với PDF từ server local (nếu có):</p>
    [pdf url="http://localhost:3000/api/uploads/media/sample.pdf" fileName="Local PDF Sample"]
    
    <p><strong>Lưu ý:</strong> Để sử dụng Chrome extension, bạn cần cài đặt extension với ID: <code>mhjfbmdgcfjbbpaeojofohoefgiehjai</code></p>
  `;

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            PDF Blog Demo - Shortcode Testing
          </h1>
          
          <div className="mb-8">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800">
                    Thông tin Demo
                  </h3>
                  <div className="mt-2 text-sm text-blue-700">
                    <p>
                      Trang này demo tính năng PDF shortcode trong blog. 
                      Chrome Extension ID: <code className="bg-blue-100 px-1 py-0.5 rounded">mhjfbmdgcfjbbpaeojofohoefgiehjai</code>
                    </p>
                    <p className="mt-1">
                      URL: <code className="bg-blue-100 px-1 py-0.5 rounded">http://localhost:3000/pdf-blog-demo</code>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* PDF Shortcode Guide */}
          <PDFShortcodeGuide />

          {/* Demo Content */}
          <div className="mt-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Nội dung Demo với PDF Shortcode
            </h2>
            
            <div className="prose max-w-none">
              <ShortcodeProcessor html={sampleContent} />
            </div>
          </div>

          {/* Raw HTML Display */}
          <div className="mt-12 border-t pt-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              HTML Source Code
            </h2>
            <details className="bg-gray-50 border rounded-lg p-4">
              <summary className="cursor-pointer font-medium text-gray-700 hover:text-gray-900">
                Xem HTML source code của nội dung demo
              </summary>
              <pre className="mt-4 text-sm text-gray-600 whitespace-pre-wrap overflow-x-auto">
                {sampleContent}
              </pre>
            </details>
          </div>

          {/* Navigation */}
          <div className="mt-8 flex justify-between items-center pt-6 border-t">
            <a
              href="/"
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Về trang chủ
            </a>
            
            <div className="text-sm text-gray-500">
              Demo tạo lúc: {new Date().toLocaleString('vi-VN')}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
