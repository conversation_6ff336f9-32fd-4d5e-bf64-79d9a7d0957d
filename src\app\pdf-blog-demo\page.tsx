import dynamic from "next/dynamic";

// Dynamically import components to avoid SSR issues
const ComprehensivePDFViewer = dynamic(() => import("@/components/Widget/ComprehensivePDFViewer"), { ssr: false });
const ShortcodeProcessor = dynamic(() => import("@/components/Widget/ShortcodeProcessor"), { ssr: false });

export default function PDFBlogDemo() {
  // Sample blog content with PDF shortcodes
  const sampleContent = `
    <h2>Demo PDF Viewer trong Blog</h2>
    <p>Đây là trang demo để test tính năng PDF viewer. Bạn có thể xem PDF bằng nhiều cách khác nhau:</p>
    
    <h3>1. PDF với Advanced Viewer (mặc định)</h3>
    <p>Shortcode này sẽ sử dụng Advanced PDF viewer với Google Docs:</p>
    [pdf url="https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf" fileName="Dummy PDF Document"]
    
    <h3>2. PDF với React PDF Viewer</h3>
    <p>Shortcode này sẽ sử dụng React PDF để hiển thị PDF tích hợp trong trang:</p>
    [pdf url="https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf" fileName="React PDF Demo" viewer="react-pdf"]
    
    <h3>3. PDF mở trong trình duyệt</h3>
    <p>Shortcode này sẽ mở PDF trong tab mới của trình duyệt:</p>
    [pdf url="https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf" fileName="Browser PDF Demo" viewer="browser"]
    
    <p><strong>Lưu ý:</strong> Advanced viewer sử dụng Google Docs để hiển thị PDF, không cần cài đặt extension.</p>
  `;

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            PDF Blog Demo - Advanced Viewer
          </h1>
          
          <div className="mb-8">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-green-800">
                    Không cần Chrome Extension
                  </h3>
                  <div className="mt-2 text-sm text-green-700">
                    <p>
                      PDF viewer này hoạt động trực tiếp trong trình duyệt mà không cần cài đặt extension.
                      Sử dụng Google Docs viewer để hiển thị PDF với khả năng tương thích cao.
                    </p>
                    <p className="mt-1">
                      URL: <code className="bg-green-100 px-1 py-0.5 rounded">http://localhost:3000/pdf-blog-demo</code>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Direct PDF Viewer Demo */}
          <div className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Demo trực tiếp PDF Viewer
            </h2>
            <ComprehensivePDFViewer 
              fileUrl="https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"
              fileName="Sample PDF Document"
            />
          </div>

          {/* Demo Content with Shortcodes */}
          <div className="mt-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Demo Shortcode trong nội dung Blog
            </h2>
            
            <div className="prose max-w-none">
              <ShortcodeProcessor html={sampleContent} />
            </div>
          </div>

          {/* Usage Guide */}
          <div className="mt-12 border-t pt-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Hướng dẫn sử dụng
            </h2>
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="font-semibold mb-3">Shortcode cơ bản:</h3>
              <code className="bg-gray-800 text-green-400 p-2 rounded block mb-4">
                [pdf url="URL_TO_PDF" fileName="Tên file" viewer="advanced"]
              </code>
              
              <h3 className="font-semibold mb-3">Các tùy chọn viewer:</h3>
              <ul className="list-disc list-inside space-y-2 text-sm text-gray-600">
                <li><strong>advanced</strong>: Sử dụng Google Docs viewer (khuyến nghị)</li>
                <li><strong>react-pdf</strong>: Hiển thị PDF tích hợp với React PDF</li>
                <li><strong>browser</strong>: Mở PDF trong tab mới</li>
              </ul>
            </div>
          </div>

          {/* Navigation */}
          <div className="mt-8 flex justify-between items-center pt-6 border-t">
            <a
              href="/"
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Về trang chủ
            </a>
            
            <div className="text-sm text-gray-500">
              Demo tạo lúc: {new Date().toLocaleString('vi-VN')}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
