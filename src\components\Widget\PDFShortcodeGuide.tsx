"use client";

import { useState } from "react";

const PDFShortcodeGuide = () => {
  const [isOpen, setIsOpen] = useState(false);

  const examples = [
    {
      title: "Cơ bản",
      code: '[pdf url="https://example.com/document.pdf"]',
      description: "Hiển thị PDF với Chrome extension làm mặc định"
    },
    {
      title: "Với tên file",
      code: '[pdf url="https://example.com/document.pdf" fileName="Tài liệu quan trọng.pdf"]',
      description: "Hiển thị PDF với tên file tùy chỉnh"
    },
    {
      title: "Chỉ định viewer",
      code: '[pdf url="https://example.com/document.pdf" viewer="react-pdf"]',
      description: "Sử dụng React PDF viewer thay vì extension"
    },
    {
      title: "Đầy đủ tùy chọn",
      code: '[pdf url="https://example.com/document.pdf" fileName="Báo cáo.pdf" viewer="extension"]',
      description: "Sử dụng tất cả tùy chọn có sẵn"
    }
  ];

  const viewerOptions = [
    { value: "extension", label: "Chrome Extension", description: "Sử dụng Chrome extension (khuyến nghị)" },
    { value: "react-pdf", label: "React PDF", description: "Hiển thị PDF tích hợp trong trang" },
    { value: "browser", label: "Trình duyệt", description: "Mở PDF trong tab mới" }
  ];

  return (
    <div className="my-6">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full p-4 text-left bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      >
        <div className="flex items-center">
          <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span className="text-sm font-medium text-blue-900">
            Hướng dẫn sử dụng PDF Shortcode
          </span>
        </div>
        <svg 
          className={`w-5 h-5 text-blue-600 transform transition-transform ${isOpen ? 'rotate-180' : ''}`} 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <div className="mt-4 p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="space-y-6">
            {/* Basic Usage */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Cách sử dụng cơ bản</h3>
              <p className="text-sm text-gray-600 mb-4">
                Để hiển thị PDF trong bài viết, sử dụng shortcode <code className="bg-gray-100 px-1 py-0.5 rounded">[pdf]</code> với các tham số sau:
              </p>
              
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Tham số:</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li><strong>url</strong> (bắt buộc): Đường dẫn đến file PDF</li>
                  <li><strong>fileName</strong> (tùy chọn): Tên hiển thị của file</li>
                  <li><strong>viewer</strong> (tùy chọn): Loại viewer sử dụng (extension, react-pdf, browser)</li>
                </ul>
              </div>
            </div>

            {/* Examples */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Ví dụ</h3>
              <div className="space-y-4">
                {examples.map((example, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">{example.title}</h4>
                    <div className="bg-gray-900 text-green-400 p-3 rounded text-sm font-mono mb-2 overflow-x-auto">
                      {example.code}
                    </div>
                    <p className="text-sm text-gray-600">{example.description}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Viewer Options */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Tùy chọn Viewer</h3>
              <div className="space-y-3">
                {viewerOptions.map((option, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                    <div className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {option.label} <code className="text-xs bg-gray-200 px-1 py-0.5 rounded ml-1">{option.value}</code>
                      </h4>
                      <p className="text-sm text-gray-600">{option.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Chrome Extension Info */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">
                    Lưu ý về Chrome Extension
                  </h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    <p>
                      Chrome Extension ID: <code className="bg-yellow-100 px-1 py-0.5 rounded">mhjfbmdgcfjbbpaeojofohoefgiehjai</code>
                    </p>
                    <p className="mt-1">
                      Extension sẽ được sử dụng để xem PDF với URL: 
                      <code className="bg-yellow-100 px-1 py-0.5 rounded ml-1">
                        chrome-extension://mhjfbmdgcfjbbpaeojofohoefgiehjai/efa3a366-2a07-43c4-a9ec-033d8fb022c9
                      </code>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PDFShortcodeGuide;
