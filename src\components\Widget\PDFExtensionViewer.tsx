"use client";

import { useState, useEffect } from "react";

interface PDFExtensionViewerProps {
  fileUrl: string;
  fileName?: string;
}

const PDFExtensionViewer = ({ fileUrl, fileName }: PDFExtensionViewerProps) => {
  const [isExtensionAvailable, setIsExtensionAvailable] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if the Chrome extension is available
    const checkExtension = () => {
      try {
        // Try to detect if the extension is installed
        const extensionId = "mhjfbmdgcfjbbpaeojofohoefgiehjai";
        const extensionUrl = `chrome-extension://${extensionId}/`;

        // Create a test image to check if extension is available
        const img = new Image();
        img.onload = () => {
          setIsExtensionAvailable(true);
          setIsLoading(false);
        };
        img.onerror = () => {
          setIsExtensionAvailable(false);
          setIsLoading(false);
        };
        img.src = `${extensionUrl}icon.png`;
      } catch (error) {
        setIsExtensionAvailable(false);
        setIsLoading(false);
      }
    };

    checkExtension();
  }, []);

  const openWithExtension = () => {
    const extensionId = "mhjfbmdgcfjbbpaeojofohoefgiehjai";
    const extensionUrl = `chrome-extension://${extensionId}/efa3a366-2a07-43c4-a9ec-033d8fb022c9`;

    // Open the extension with the PDF URL
    window.open(`${extensionUrl}?url=${encodeURIComponent(fileUrl)}`, '_blank');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8 bg-gray-50 rounded-lg">
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="text-gray-600">Đang kiểm tra extension...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full my-4">
      <div className="bg-white border rounded-lg p-6 shadow-sm">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Xem PDF với Chrome Extension
            </h3>
            {fileName && (
              <p className="text-sm text-gray-600 mt-1">{fileName}</p>
            )}
          </div>

          {isExtensionAvailable ? (
            <div className="flex items-center space-x-2">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Extension có sẵn
              </span>
            </div>
          ) : (
            <div className="flex items-center space-x-2">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                Extension không có sẵn
              </span>
            </div>
          )}
        </div>

        <div className="space-y-4">
          {isExtensionAvailable ? (
            <div>
              <button
                onClick={openWithExtension}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                Mở PDF với Extension
              </button>
              <p className="text-xs text-gray-500 mt-2">
                Nhấp để mở PDF trong tab mới với Chrome extension
              </p>
            </div>
          ) : (
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">
                    Chrome Extension không có sẵn
                  </h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    <p>
                      Để sử dụng tính năng xem PDF nâng cao, vui lòng cài đặt Chrome extension.
                    </p>
                  </div>
                  <div className="mt-4">
                    <div className="-mx-2 -my-1.5 flex">
                      <a
                        href={`chrome-extension://mhjfbmdgcfjbbpaeojofohoefgiehjai/efa3a366-2a07-43c4-a9ec-033d8fb022c9?url=${encodeURIComponent(fileUrl)}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-yellow-50 px-2 py-1.5 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-yellow-50 focus:ring-yellow-600"
                      >
                        Thử mở với Extension
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="border-t pt-4">
            <p className="text-sm text-gray-600">
              <strong>URL PDF:</strong>
              <a
                href={fileUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 ml-2 break-all"
              >
                {fileUrl}
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PDFExtensionViewer;