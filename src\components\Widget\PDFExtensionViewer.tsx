"use client";

import { useState } from "react";

interface AdvancedPDFViewerProps {
  fileUrl: string;
  fileName?: string;
}

const AdvancedPDFViewer = ({ fileUrl, fileName }: AdvancedPDFViewerProps) => {
  const [viewerType, setViewerType] = useState<'iframe' | 'embed' | 'object' | 'google' | 'download'>('google');
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Create different viewer URLs
  const getViewerUrl = () => {
    switch (viewerType) {
      case 'iframe':
        // Use browser's built-in PDF viewer
        return fileUrl;
      case 'embed':
        return fileUrl;
      case 'object':
        return fileUrl;
      case 'google':
        // Use Google Docs viewer for better compatibility
        return `https://docs.google.com/gview?embedded=true&url=${encodeURIComponent(fileUrl)}`;
      default:
        return fileUrl;
    }
  };

  const handleLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  const renderViewer = () => {
    const viewerUrl = getViewerUrl();

    if (viewerType === 'download') {
      return (
        <div className="text-center py-12">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-4-4m4 4l4-4m-4-4V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">Tải xuống PDF</h3>
          <p className="mt-1 text-sm text-gray-500">
            Nhấp vào nút bên dưới để tải xuống và xem PDF
          </p>
          <div className="mt-6">
            <a
              href={fileUrl}
              download={fileName}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-4-4m4 4l4-4m-4-4V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2" />
              </svg>
              Tải xuống PDF
            </a>
          </div>
        </div>
      );
    }

    return (
      <div className="relative" style={{ height: '600px' }}>
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-10">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="text-gray-600">Đang tải PDF...</span>
            </div>
          </div>
        )}

        {hasError && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-50 z-10">
            <div className="text-center">
              <svg className="mx-auto h-12 w-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">Không thể tải PDF</h3>
              <p className="mt-1 text-sm text-gray-500">
                Thử chuyển sang phương thức khác hoặc tải xuống file
              </p>
              <div className="mt-4">
                <a
                  href={fileUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Mở trong tab mới
                </a>
              </div>
            </div>
          </div>
        )}

        {(viewerType === 'iframe' || viewerType === 'google') && (
          <iframe
            src={viewerUrl}
            className="w-full h-full border-0 rounded"
            onLoad={handleLoad}
            onError={handleError}
            title={fileName || 'PDF Viewer'}
            allow="fullscreen"
          />
        )}

        {viewerType === 'embed' && (
          <embed
            src={viewerUrl}
            type="application/pdf"
            className="w-full h-full rounded"
            onLoad={handleLoad}
            onError={handleError}
          />
        )}

        {viewerType === 'object' && (
          <object
            data={viewerUrl}
            type="application/pdf"
            className="w-full h-full rounded"
            onLoad={handleLoad}
            onError={handleError}
          >
            <p className="text-center py-8">
              Trình duyệt không hỗ trợ hiển thị PDF.
              <a href={fileUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 ml-1">
                Nhấp vào đây để mở PDF
              </a>
            </p>
          </object>
        )}
      </div>
    );
  };

  return (
    <div className="w-full my-4">
      <div className="bg-white border rounded-lg shadow-sm overflow-hidden">
        {/* Header with controls */}
        <div className="bg-gray-50 px-4 py-3 border-b">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">
                PDF Viewer
              </h3>
              {fileName && (
                <p className="text-xs text-gray-600 mt-1">{fileName}</p>
              )}
            </div>

            <div className="flex items-center space-x-2">
              {/* Viewer type selector */}
              <select
                value={viewerType}
                onChange={(e) => {
                  setViewerType(e.target.value as any);
                  setIsLoading(true);
                  setHasError(false);
                }}
                className="text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="google">Google Docs</option>
                <option value="iframe">IFrame</option>
                <option value="embed">Embed</option>
                <option value="object">Object</option>
                <option value="download">Tải xuống</option>
              </select>

              <a
                href={fileUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                Mở mới
              </a>
            </div>
          </div>
        </div>

        {/* PDF Viewer */}
        <div className="p-4">
          {renderViewer()}
        </div>

        {/* Footer with URL */}
        <div className="bg-gray-50 px-4 py-2 border-t">
          <p className="text-xs text-gray-600 truncate">
            <strong>URL:</strong> {fileUrl}
          </p>
        </div>
      </div>
    </div>
  );
};

export default AdvancedPDFViewer;