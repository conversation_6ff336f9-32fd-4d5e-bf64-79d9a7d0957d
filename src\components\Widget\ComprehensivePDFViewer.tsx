"use client";

import { useState } from "react";
import dynamic from "next/dynamic";

// Dynamically import PDF viewers to avoid SSR issues
const PdfViewer = dynamic(() => import("@/components/Widget/PdfView"), { ssr: false });
const AdvancedPDFViewer = dynamic(() => import("@/components/Widget/PDFExtensionViewer"), { ssr: false });

interface ComprehensivePDFViewerProps {
  fileUrl: string;
  fileName?: string;
}

type ViewerType = 'react-pdf' | 'advanced' | 'browser';

const ComprehensivePDFViewer = ({ fileUrl, fileName }: ComprehensivePDFViewerProps) => {
  const [selectedViewer, setSelectedViewer] = useState<ViewerType>('advanced');

  const viewerOptions = [
    {
      id: 'advanced' as ViewerType,
      name: 'Advanced Viewer',
      description: 'Xem PDF tích hợp với nhiều tùy chọn (khu<PERSON>ến nghị)',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
        </svg>
      )
    },
    {
      id: 'react-pdf' as ViewerType,
      name: 'React PDF',
      description: 'Xem PDF tích hợp trong trang',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      )
    },
    {
      id: 'browser' as ViewerType,
      name: 'Trình duyệt',
      description: 'Mở PDF trong tab mới của trình duyệt',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
        </svg>
      )
    }
  ];

  const renderViewer = () => {
    switch (selectedViewer) {
      case 'advanced':
        return <AdvancedPDFViewer fileUrl={fileUrl} fileName={fileName} />;
      case 'react-pdf':
        return <PdfViewer fileUrl={fileUrl} />;
      case 'browser':
        return (
          <div className="w-full my-4">
            <div className="bg-white border rounded-lg p-6 shadow-sm">
              <div className="text-center">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">Mở PDF trong trình duyệt</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Nhấp vào nút bên dưới để mở PDF trong tab mới
                </p>
                <div className="mt-6">
                  <a
                    href={fileUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                    Mở PDF
                  </a>
                </div>
                {fileName && (
                  <p className="mt-2 text-xs text-gray-500">{fileName}</p>
                )}
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="w-full my-4">
      {/* Viewer Selection */}
      <div className="mb-4">
        <div className="bg-white border rounded-lg p-4 shadow-sm">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Chọn cách xem PDF:</h3>
          <div className="grid grid-cols-1 gap-3 sm:grid-cols-3">
            {viewerOptions.map((option) => (
              <button
                key={option.id}
                onClick={() => setSelectedViewer(option.id)}
                className={`relative rounded-lg border p-3 text-left focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  selectedViewer === option.id
                    ? 'border-blue-600 bg-blue-50'
                    : 'border-gray-300 bg-white hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center">
                  <div className={`flex-shrink-0 ${
                    selectedViewer === option.id ? 'text-blue-600' : 'text-gray-400'
                  }`}>
                    {option.icon}
                  </div>
                  <div className="ml-3">
                    <p className={`text-sm font-medium ${
                      selectedViewer === option.id ? 'text-blue-900' : 'text-gray-900'
                    }`}>
                      {option.name}
                    </p>
                    <p className={`text-xs ${
                      selectedViewer === option.id ? 'text-blue-700' : 'text-gray-500'
                    }`}>
                      {option.description}
                    </p>
                  </div>
                </div>
                {selectedViewer === option.id && (
                  <div className="absolute -inset-px rounded-lg border-2 border-blue-600 pointer-events-none" />
                )}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Selected Viewer */}
      {renderViewer()}
    </div>
  );
};

export default ComprehensivePDFViewer;