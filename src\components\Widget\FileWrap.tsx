"use client";
import { useEffect, useState } from "react";
import dynamic from "next/dynamic";
import envConfig from "@/config";
import FileViewerErrorBoundary from "./FileViewerErrorBoundary";

// Load ExcelViewer, PdfViewer and DocumentViewer dynamically (client-side only)
const ExcelViewer = dynamic(() => import("@/components/Widget/ExcelViewer"), { ssr: false });
const PdfViewer = dynamic(() => import("@/components/Widget/PdfView"), { ssr: false });
const DocumentViewer = dynamic(() => import("@/components/Widget/DocumentViewer"), { ssr: false });

const FileWrap = ({ blog }: { blog: any }) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // ✅ Detect mobile safely
    const userAgent = navigator.userAgent || navigator.vendor;
    setIsMobile(/iPhone|iPad|iPod|Android/i.test(userAgent));
  }, []);

  // Debug logging
  console.log("FileWrap received blog:", blog);
  console.log("FileWrap blog.file:", blog?.file);
  console.log("FileWrap blog.file length:", blog?.file?.length);

  // Debug each file item
  if (blog?.file) {
    blog.file.forEach((fileItem: any, index: number) => {
      console.log(`File ${index}:`, fileItem);
      console.log(`File ${index} path:`, fileItem?.path);
      console.log(`File ${index} type:`, fileItem?.type);
    });
  }

  if (!blog?.file || blog.file.length === 0) {
    console.log("FileWrap: No files to display, returning null");
    return null; // ✅ Check for empty array
  }

  const allowedIframeTypes = ["pdf", "docx", "doc", "pptx", "ppt"];
  const googleViewerTypes = ["docx", "doc", "pptx", "ppt"];

  return (
    <div className="file-wrap relative space-y-4">
      {blog.file.map((fileItem: any, index: number) => {
        // Check if fileItem and fileItem.path exist
        if (!fileItem || !fileItem.path) {
          console.warn(`Invalid file item at index ${index}:`, fileItem);
          return null;
        }

        // Construct proper file URL
        const fileUrl = fileItem.path.startsWith('http')
          ? fileItem.path
          : `${envConfig.NEXT_PUBLIC_API_ENDPOINT}${fileItem.path}`;

        const fileName = fileItem.originalName || fileItem.path.split('/').pop() || 'Unknown File';
        
        // Better file extension detection
        let fileExt = "";
        if (fileItem.type) {
          // Use mimetype to determine file type more accurately
          if (fileItem.type.includes('pdf')) fileExt = 'pdf';
          else if (fileItem.type.includes('excel') || fileItem.type.includes('spreadsheet')) fileExt = 'xlsx';
          else if (fileItem.type.includes('word') || fileItem.type.includes('document')) fileExt = 'docx';
          else if (fileItem.type.includes('powerpoint') || fileItem.type.includes('presentation')) fileExt = 'pptx';
          else fileExt = (fileItem.type.split('/')[1] || fileItem.path.split(".").pop())?.toLowerCase() || "";
        } else {
          fileExt = (fileItem.path.split(".").pop())?.toLowerCase() || "";
        }

        console.log(`Processing file ${index}:`, {
          fileUrl,
          fileName,
          fileExt,
          originalType: fileItem.type
        });

        return (
          <div key={index} className="max-w-[680px] w-full">
            <FileViewerErrorBoundary fileName={fileName}>
              {fileExt === "xlsx" || fileExt === "xls" ? (
                <ExcelViewer fileUrl={fileUrl} />
              ) : fileExt === "pdf" ? (
                <PdfViewer fileUrl={fileUrl} />
              ) : googleViewerTypes.includes(fileExt) || allowedIframeTypes.includes(fileExt) ? (
                <DocumentViewer 
                  fileUrl={fileUrl} 
                  fileName={fileName} 
                  fileExt={fileExt} 
                />
              ) : (
                <div className="p-4 border rounded bg-gray-50">
                  <p className="text-gray-600">File type not supported for preview: {fileExt}</p>
                  <p className="text-sm text-gray-500">Use the download button below to access the file.</p>
                </div>
              )}
            </FileViewerErrorBoundary>
            <div className="flex items-center justify-between mt-4 border p-4 rounded bg-white">
              <div>
                <span className="text-sm text-gray-700">File Đính kèm: </span>
                <span className="text-sm font-medium">{fileName}</span>
                {fileItem.size && (
                  <span className="text-xs text-gray-500 ml-2">
                    ({Math.round(fileItem.size / 1024)} KB)
                  </span>
                )}
              </div>
              <a
                href={fileUrl}
                download={fileName}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Download File
              </a>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default FileWrap;
